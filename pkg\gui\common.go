package gui

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/dialog"
	"github.com/golang/glog"
)

func showInfoDialog(info string, parent fyne.Window) {
	dialog.ShowInformation("提示", info, parent)
}

func showConfirmDialog(info string, parent fyne.Window) bool {
	resultchan := make(chan bool)
	go dialog.ShowConfirm("确认", info, func(b bool) {
		resultchan <- b
	}, parent)

	flag := <-resultchan
	glog.Infof("common gui: got confirm flag:%v", flag)
	return flag
}
