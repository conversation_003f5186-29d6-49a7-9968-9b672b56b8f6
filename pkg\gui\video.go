package gui

import (
	"fmt"
	"image/color"
	"os"
	"sync"
	"sync/atomic"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/canvas"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/layout"
	"fyne.io/fyne/v2/widget"
	"github.com/hajimehoshi/go-mp3"
	"github.com/hajimehoshi/oto/v2"
)

var (
	// 加载PNG图片
	imagePaths = []string{
		"resources/scene1/male_lead_speak1.png",
		"resources/scene1/male_lead_speak2.png",
		"resources/scene1/male_lead_speak3.png",
	}

	speechtext1 = "虽然前方拥堵，您仍然在最优道路上"

	localFrameCounter int32
)

func NewCanvas(parent fyne.Window) fyne.CanvasObject {
	// 定义对象大小
	objectSize := float32(600)
	ballSize := float32(40)

	// // 加载PNG图片
	// imagePaths := []string{
	// 	"resources/scene1/close.png",
	// 	"resources/scene1/halfopen.png",
	// 	"resources/scene1/open.png",
	// }

	var images []*canvas.Image
	for _, path := range imagePaths {
		img := canvas.NewImageFromFile(path)
		if img != nil {
			img.FillMode = canvas.ImageFillContain
			img.Resize(fyne.NewSize(objectSize, objectSize))
			img.Move(fyne.NewPos(50, 50))
			images = append(images, img)
		} else {
			fmt.Printf("加载图片失败: %s\n", path)
		}
	}

	// 如果没有成功加载图片，创建一个默认的小球
	var currentDisplayObject fyne.CanvasObject
	if len(images) > 0 {
		currentDisplayObject = images[0]
	} else {
		// 创建小球作为备用
		ball := canvas.NewCircle(color.NRGBA{R: 0, G: 150, B: 255, A: 255})
		ball.Resize(fyne.NewSize(ballSize, ballSize))
		ball.Move(fyne.NewPos(50, 50))
		currentDisplayObject = ball
	}

	// 创建动画区域边框
	border := canvas.NewRectangle(color.Transparent)
	border.StrokeColor = color.NRGBA{R: 0, G: 0, B: 0, A: 255} // 黑色边框
	border.StrokeWidth = 2                                     // 边框宽度
	border.FillColor = color.Transparent                       // 透明填充

	// 创建容器并添加边框和当前显示对象
	content := container.NewWithoutLayout(border, currentDisplayObject)

	// 创建控制按钮
	startBtn := widget.NewButton("开始动画", nil)
	stopBtn := widget.NewButton("停止动画", nil)
	resetBtn := widget.NewButton("重置位置", nil)
	speedSlider := widget.NewSlider(1, 20)
	speedSlider.Value = 5
	speedLabel := widget.NewLabel("速度: 5")

	// 添加状态标签显示位置
	posLabel := widget.NewLabel(fmt.Sprintf("位置: (%.1f, %.1f)", 50.0, 50.0))

	// 创建语音文本显示区域
	speechLabel := widget.NewLabel(speechtext1)
	speechLabel.Wrapping = fyne.TextWrapWord     // 启用文本换行
	speechLabel.Alignment = fyne.TextAlignCenter // 居中对齐

	// // 创建语音内容标题
	// speechTitle := widget.NewLabel("语音内容:")
	// speechTitle.TextStyle = fyne.TextStyle{Bold: true} // 加粗标题

	// 创建语音文本容器，添加一些样式和间距
	speechContainer := container.NewVBox(
		widget.NewSeparator(), // 添加分隔线
		// speechTitle,
		speechLabel,
		widget.NewLabel(""), // 添加底部间距
	)

	// 按钮容器
	controls := container.NewVBox(
		container.NewHBox(
			startBtn,
			stopBtn,
			resetBtn,
			layout.NewSpacer(),
		),
		container.NewHBox(
			widget.NewLabel("速度调节:"),
			speedSlider,
			speedLabel,
		),
		posLabel, // 位置显示
	)

	// 主布局（控制按钮在上，动画区域在中间，语音文本在下）
	mainContainer := container.NewBorder(controls, speechContainer, nil, nil, content)

	// 动画控制变量
	var (
		// dx, dy          float32               = 3.0, 4.0 // 初始速度
		x, y         float32               = 50, 50 // 初始位置
		running      bool                           // 动画运行状态
		mu           sync.Mutex                     // 用于安全访问共享变量
		stopCh       = make(chan struct{})          // 停止信号通道
		imageIndex   int                            // 当前显示的图片索引
		frameCounter int                            // 帧计数器，用于控制图片切换频率

		// 音频相关变量
		audioContext *oto.Context
		audioPlayer  oto.Player
		audioMu      sync.Mutex // 音频操作的互斥锁
	)

	// 播放MP3音频 - 修复版本，根据MP3文件参数动态设置
	playAudio := func(filename string) error {
		audioMu.Lock()
		defer audioMu.Unlock()

		// 停止当前播放的音频
		if audioPlayer != nil {
			audioPlayer.Close()
			audioPlayer = nil
		}

		// 打开MP3文件
		file, err := os.Open(filename)
		if err != nil {
			fmt.Printf("打开音频文件失败: %v\n", err)
			return err
		}

		// 解码MP3
		decoder, err := mp3.NewDecoder(file)
		if err != nil {
			file.Close()
			fmt.Printf("解码MP3文件失败: %v\n", err)
			return err
		}

		// 获取MP3文件的音频参数
		sampleRate := decoder.SampleRate()
		fmt.Printf("MP3文件参数 - 采样率: %d Hz, 长度: %d 样本\n", sampleRate, decoder.Length())

		// 总是重新创建音频上下文以确保参数正确
		if audioContext != nil {
			// 如果已有上下文，先清理
			audioContext = nil
		}

		// 根据MP3文件的实际参数创建音频上下文
		// 参数：采样率，声道数(2=立体声)，样本大小(2字节=16位)
		audioContext, ready, err := oto.NewContext(sampleRate, 2, 2)
		if err != nil {
			file.Close()
			fmt.Printf("创建音频上下文失败: %v\n", err)
			return err
		}

		// 等待音频上下文准备就绪
		<-ready
		fmt.Printf("音频上下文创建成功，采样率: %d Hz\n", sampleRate)

		// 创建音频播放器
		audioPlayer = audioContext.NewPlayer(decoder)

		// 在新的goroutine中播放音频
		go func() {
			defer func() {
				audioMu.Lock()
				if audioPlayer != nil {
					audioPlayer.Close()
					audioPlayer = nil
				}
				audioMu.Unlock()
				file.Close()
			}()

			audioPlayer.Play()

			// 等待播放完成
			for audioPlayer.IsPlaying() {
				time.Sleep(time.Millisecond * 100)
			}

			fmt.Printf("音频播放完成\n")
		}()

		return nil
	}

	// 停止音频播放
	stopAudio := func() {
		audioMu.Lock()
		defer audioMu.Unlock()
		if audioPlayer != nil {
			audioPlayer.Close()
			audioPlayer = nil
		}
	}

	// 更新边框大小函数
	updateBorderSize := func() {
		// 获取窗口尺寸
		windowWidth := float32(parent.Canvas().Size().Width - 8)
		windowHeight := float32(parent.Canvas().Size().Height - 154)

		// 设置边框位置和大小
		border.Resize(fyne.NewSize(windowWidth, windowHeight))
		border.Move(fyne.NewPos(0, 0))
	}

	// // 精确边界检测函数
	// checkBoundaries := func() {
	// 	// 获取窗口尺寸
	// 	// TODO : 这儿的尺寸不精确，后续考虑优化
	// 	windowWidth := float32(parent.Canvas().Size().Width - 8)
	// 	windowHeight := float32(parent.Canvas().Size().Height - 154)

	// 	// 根据当前显示对象确定大小
	// 	var objSize float32
	// 	if len(images) > 0 {
	// 		objSize = objectSize
	// 	} else {
	// 		objSize = ballSize
	// 	}

	// 	// 检查左右边界
	// 	if x < 0 {
	// 		x = 0
	// 		dx = -dx
	// 	} else if x > windowWidth-objSize {
	// 		x = windowWidth - objSize
	// 		dx = -dx
	// 	}

	// 	// 检查上下边界
	// 	if y < 0 {
	// 		y = 0
	// 		dy = -dy
	// 	} else if y > windowHeight-objSize {
	// 		y = windowHeight - objSize
	// 		dy = -dy
	// 	}
	// }

	startTime := time.Now()

	// 动画循环函数 - 激进优化版本
	animate := func() {
		// 预分配变量避免GC压力和减少系统调用
		var pos fyne.Position
		var lastImageSwitchFrame int32

		// 使用更高精度的定时器，减少系统调用开销
		ticker := time.NewTicker(time.Millisecond * 16)
		defer ticker.Stop()

		for {
			select {
			case <-stopCh:
				return // 收到停止信号，退出goroutine
			case <-ticker.C:
				// 原子递增帧计数器，避免锁竞争
				atomic.AddInt32(&localFrameCounter, 1)
				currentFrame := atomic.LoadInt32(&localFrameCounter)

				// 最小化锁的持有时间
				mu.Lock()
				isRunning := running
				currentX, currentY := x, y
				mu.Unlock()

				if !isRunning {
					continue
				}

				// 批量处理策略：只在特定帧执行昂贵操作

				// 1. 图片切换逻辑 - 每30帧（约0.5秒）切换一次，减少DOM操作
				if len(images) > 0 && currentFrame-lastImageSwitchFrame >= 30 {
					mu.Lock()
					imageIndex = (imageIndex + 1) % len(images)

					// 最小化DOM操作：只在真正需要时切换
					if currentDisplayObject != images[imageIndex] {
						content.Remove(currentDisplayObject)
						currentDisplayObject = images[imageIndex]
						pos.X, pos.Y = currentX, currentY
						currentDisplayObject.Move(pos)
						content.Add(currentDisplayObject)
						currentDisplayObject.Refresh()
						lastImageSwitchFrame = currentFrame
					}
					mu.Unlock()
				} else {
					// // 2. 位置更新 - 每帧都更新位置（保持流畅），但大幅减少刷新
					// pos.X, pos.Y = currentX, currentY
					// currentDisplayObject.Move(pos)

					// // 3. 刷新控制 - 每30帧刷新一次（约0.5秒），大幅减少重绘
					// if currentFrame-lastRefreshFrame >= 30 {
					// 	currentDisplayObject.Refresh()
					// 	lastRefreshFrame = currentFrame
					// }
				}
			}
		}
	}

	freshdebugtext := func() {
		// 使用更高精度的定时器，减少系统调用开销
		ticker := time.NewTicker(time.Millisecond * 1000)
		defer ticker.Stop()

		for {
			select {
			case <-stopCh:
				return // 收到停止信号，退出goroutine
			case <-ticker.C:
				if !running {
					continue
				}

				mu.Lock()
				currentFrame := atomic.LoadInt32(&localFrameCounter)
				frameCounter = int(currentFrame)
				// 极简化的字符串格式化，减少内存分配
				seconds := time.Since(startTime).Seconds()
				posLabel.SetText(fmt.Sprintf("帧:%d,seconds:%.1f,帧率:%.1f", frameCounter, seconds, float64(frameCounter)/seconds))
				mu.Unlock()
			}
		}
	}

	// 启动动画goroutine
	go animate()
	go freshdebugtext()

	// 按钮事件处理
	startBtn.OnTapped = func() {
		mu.Lock()
		running = true
		mu.Unlock()
		startBtn.Disable()
		stopBtn.Enable()

		// 更新语音文本显示状态
		// speechTitle.SetText("正在播放语音:")
		speechLabel.SetText("🔊 " + speechtext1) // 添加音频图标

		// TODO : 判断是否选择了需要渲染的yaml，如果没有，则提示选择，本次不做任何处理
		// // 在goroutine中调用selectFile避免阻塞GUI主线程
		// go func() {
		// 	f, err := selectFile()
		// 	if err == nil && f != "" {
		// 		fmt.Println("onstart 选择的文件路径:", f)
		// 	} else if err != nil {
		// 		fmt.Printf("文件选择错误: %v\n", err)
		// 	}
		// }()

		// 播放音频
		go func() {
			if err := playAudio("resources/scene1/speech1.mp3"); err != nil {
				fmt.Printf("播放音频失败: %v\n", err)
			}
		}()
	}

	stopBtn.OnTapped = func() {
		mu.Lock()
		running = false
		mu.Unlock()
		stopBtn.Disable()
		startBtn.Enable()

		// 停止音频播放
		stopAudio()

		// 更新语音文本显示状态
		// speechTitle.SetText("语音内容:")
		speechLabel.SetText("⏹️ " + speechtext1) // 添加停止图标
	}

	resetBtn.OnTapped = func() {
		mu.Lock()
		running = false
		x, y = 50, 50
		// dx, dy = 3.0, 4.0
		imageIndex = 0
		frameCounter = 0

		// 重置显示对象位置
		if len(images) > 0 {
			// 移除当前显示对象
			content.Remove(currentDisplayObject)
			// 重置为第一张图片
			currentDisplayObject = images[0]
			currentDisplayObject.Move(fyne.NewPos(x, y))
			// 重新添加到容器
			content.Add(currentDisplayObject)
		} else {
			currentDisplayObject.Move(fyne.NewPos(x, y))
		}

		posLabel.SetText(fmt.Sprintf("位置: (%.1f, %.1f)", x, y))
		updateBorderSize() // 重置时更新边框大小
		mu.Unlock()

		// 停止音频播放
		stopAudio()

		// 重置语音文本显示状态
		// speechTitle.SetText("语音内容:")
		speechLabel.SetText(speechtext1) // 恢复原始文本

		currentDisplayObject.Refresh()
		startBtn.Enable()
		stopBtn.Disable()
	}

	// 速度滑块事件
	speedSlider.OnChanged = func(value float64) {
		mu.Lock()
		speedLabel.SetText(fmt.Sprintf("速度: %.1f", value))
		mu.Unlock()
	}

	// 窗口大小变化处理
	parent.Canvas().SetOnTypedKey(func(k *fyne.KeyEvent) {
		if k.Name == fyne.KeyF11 {
			parent.SetFullScreen(!parent.FullScreen())
			// 更新边框大小
			updateBorderSize()
		}
	})

	// 添加窗口大小变化监听器
	var lastWindowSize fyne.Size
	lastWindowSize = parent.Canvas().Size()

	// 创建一个定时器来检查窗口大小变化（降低检查频率）
	go func() {
		ticker := time.NewTicker(time.Millisecond * 100) // 每100ms检查一次
		defer ticker.Stop()

		for {
			select {
			case <-stopCh:
				return
			case <-ticker.C:
				currentSize := parent.Canvas().Size()
				if currentSize.Width != lastWindowSize.Width || currentSize.Height != lastWindowSize.Height {
					updateBorderSize()
					lastWindowSize = currentSize
				}
			}
		}
	}()

	// 初始更新边框大小
	updateBorderSize()

	// 初始按钮状态
	stopBtn.Disable()

	// 窗口关闭时清理资源
	parent.SetOnClosed(func() {
		close(stopCh) // 关闭通道，通知动画goroutine退出
		stopAudio()   // 停止音频播放
		// 注意：oto.Context 不需要手动关闭，会自动清理
	})

	return mainContainer
}
